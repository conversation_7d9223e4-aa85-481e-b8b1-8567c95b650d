

![Video Thumbnail](https://img.youtube.com/vi/EzzcEL_1o9o/maxresdefault.jpg)

[Video tutorial](https://youtu.be/EzzcEL_1o9o)

The frontend is [here](https://github.com/wass08/r3f-virtual-girlfriend-frontend).

## Setup

### 1. API Keys Configuration
Create a `.env` file at the root of the repository to add your **Google Generative AI** and **ElevenLabs API Keys**. Refer to `.env.example` for the environment variable names.

**Required API Keys:**
- **Google Generative AI API Key**: Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
- **ElevenLabs API Key**: Get from [ElevenLabs](https://elevenlabs.io/)

Copy `.env.example` to `.env` and fill in your actual API keys:
```bash
cp .env.example .env
```

Then edit `.env` with your actual API keys:
```
GEMINI_API_KEY=your_actual_google_ai_api_key_here
ELEVEN_LABS_API_KEY=your_actual_elevenlabs_api_key_here
```

### 2. Rhubarb Lip Sync Setup
Download the **RhubarbLibrary** binary for your **OS** [here](https://github.com/DanielSWolf/rhubarb-lip-sync/releases) and put it in your `bin` folder. `rhubarb` executable should be accessible through `bin/rhubarb`.

### 3. Install Dependencies and Start

Install dependencies:
```bash
yarn
```

Test your API key configuration:
```bash
yarn test-api
```

Start the development server:
```bash
yarn dev
```

## Troubleshooting

### Google Generative AI API Key Issues
- Make sure you have a valid API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
- Ensure the API key is correctly set in your `.env` file
- Run `yarn test-api` to verify your API key is working
- Check the console output for detailed error messages

### Common Error Messages
- **"API key not valid"**: Your Google Generative AI API key is invalid or expired
- **"Please my dear, don't forget to add your API keys!"**: API keys are missing from `.env` file
