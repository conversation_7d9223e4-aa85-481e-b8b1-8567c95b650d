import { GoogleGenerativeAI } from "@google/generative-ai";
import dotenv from "dotenv";

dotenv.config();

async function testGoogleGenerativeAI() {
  console.log("🧪 Testing Google Generative AI API Key...\n");
  
  const apiKey = process.env.GEMINI_API_KEY;
  
  if (!apiKey || apiKey === "-" || apiKey === "YOUR_GOOGLE_GENERATIVE_AI_API_KEY_HERE") {
    console.log("❌ GEMINI_API_KEY is not set or invalid in .env file");
    console.log("📝 Please get your API key from: https://makersuite.google.com/app/apikey");
    console.log("⚙️  Add it to your .env file as: GEMINI_API_KEY=your_api_key_here");
    return false;
  }
  
  try {
    const genAI = new GoogleGenerativeAI({ apiKey });
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });
    
    console.log("🔄 Making test request to Gemini Pro...");
    const result = await model.generateContent("Say hello in JSON format with a 'message' field");
    
    if (result?.response?.candidates?.[0]?.content?.parts?.[0]?.text) {
      console.log("✅ Google Generative AI API key is working!");
      console.log("📄 Test response received successfully");
      return true;
    } else {
      console.log("⚠️  API key works but response format is unexpected");
      return false;
    }
  } catch (error) {
    console.log("❌ Google Generative AI API Error:", error.message);
    
    if (error.message && error.message.includes("API key not valid")) {
      console.log("🔑 Your API key is invalid or expired");
      console.log("📝 Please get a new API key from: https://makersuite.google.com/app/apikey");
    }
    
    return false;
  }
}

async function testElevenLabs() {
  console.log("\n🧪 Testing ElevenLabs API Key...\n");
  
  const apiKey = process.env.ELEVEN_LABS_API_KEY;
  
  if (!apiKey || apiKey === "YOUR_ELEVEN_LABS_API_KEY_HERE") {
    console.log("❌ ELEVEN_LABS_API_KEY is not set in .env file");
    console.log("📝 Please get your API key from: https://elevenlabs.io/");
    console.log("⚙️  Add it to your .env file as: ELEVEN_LABS_API_KEY=your_api_key_here");
    return false;
  }
  
  try {
    // Simple test to check if the API key format is valid
    // ElevenLabs API keys typically start with specific prefixes
    if (apiKey.length < 20) {
      console.log("⚠️  ElevenLabs API key seems too short");
      return false;
    }
    
    console.log("✅ ElevenLabs API key format looks valid");
    console.log("ℹ️  Note: Full ElevenLabs testing requires making actual API calls");
    return true;
  } catch (error) {
    console.log("❌ ElevenLabs API Error:", error.message);
    return false;
  }
}

async function main() {
  console.log("🔧 API Key Configuration Test\n");
  console.log("=" .repeat(50));
  
  const googleAIWorking = await testGoogleGenerativeAI();
  const elevenLabsWorking = await testElevenLabs();
  
  console.log("\n" + "=".repeat(50));
  console.log("📊 Test Results Summary:");
  console.log(`  Google Generative AI: ${googleAIWorking ? "✅ Working" : "❌ Failed"}`);
  console.log(`  ElevenLabs: ${elevenLabsWorking ? "✅ Configured" : "❌ Failed"}`);
  
  if (googleAIWorking && elevenLabsWorking) {
    console.log("\n🎉 All API keys are configured correctly!");
    console.log("🚀 You can now run 'yarn dev' to start the application");
  } else {
    console.log("\n⚠️  Please fix the API key issues above before running the application");
  }
}

main().catch(console.error);
