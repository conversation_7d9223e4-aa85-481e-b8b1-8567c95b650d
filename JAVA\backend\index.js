import { exec } from "child_process";
import { GoogleGenerativeAI } from "@google/generative-ai";
import cors from "cors";
import dotenv from "dotenv";
import voice from "elevenlabs-node";
import express from "express";
import { promises as fs } from "fs";
import OpenA<PERSON> from "openai";
dotenv.config();

const genAI = new GoogleGenerativeAI({
  apiKey: process.env.GEMINI_API_KEY || "-", // Fallback to "-" to prevent initialization errors
});

const elevenLabsApiKey = process.env.ELEVEN_LABS_API_KEY;
const voiceID = "kgG7dCoKCfLehAPWkJOE";

const app = express();
app.use(express.json());
app.use(cors());
const port = 5000;

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.get("/voices", async (req, res) => {
  res.send(await voice.getVoices(elevenLabsApiKey));
});

const execCommand = (command) => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) reject(error);
      resolve(stdout);
    });
  });
};

const lipSyncMessage = async (message) => {
  const time = new Date().getTime();
  console.log(`Starting conversion for message ${message}`);
  await execCommand(
    `ffmpeg -y -i audios/message_${message}.mp3 audios/message_${message}.wav`
    // -y to overwrite the file
  );
  console.log(`Conversion done in ${new Date().getTime() - time}ms`);
  await execCommand(
    `./bin/rhubarb -f json -o audios/message_${message}.json audios/message_${message}.wav -r phonetic`
  );
  // -r phonetic is faster but less accurate
  console.log(`Lip sync done in ${new Date().getTime() - time}ms`);
};

app.post("/chat", async (req, res) => {
  const userMessage = req.body.message;
  if (!userMessage) {
    res.send({
      messages: [
        {
          text: "Hey dear... How was your day?",
          audio: await audioFileToBase64("audios/intro_0.wav"),
          lipsync: await readJsonTranscript("audios/intro_0.json"),
          facialExpression: "smile",
          animation: "Talking_1",
        },
        {
          text: "I missed you so much... Please don't go for so long!",
          audio: await audioFileToBase64("audios/intro_1.wav"),
          lipsync: await readJsonTranscript("audios/intro_1.json"),
          facialExpression: "sad",
          animation: "Crying",
        },
      ],
    });
    return;
  }
  // Check if API keys are properly configured
  const geminiApiKey = process.env.GEMINI_API_KEY;
  if (!elevenLabsApiKey || !geminiApiKey || geminiApiKey === "-" || geminiApiKey === "YOUR_GOOGLE_GENERATIVE_AI_API_KEY_HERE") {
    console.error("❌ API Keys Missing:");
    if (!geminiApiKey || geminiApiKey === "-" || geminiApiKey === "YOUR_GOOGLE_GENERATIVE_AI_API_KEY_HERE") {
      console.error("  - GEMINI_API_KEY is not set or invalid");
      console.error("  - Get your API key from: https://makersuite.google.com/app/apikey");
    }
    if (!elevenLabsApiKey) {
      console.error("  - ELEVEN_LABS_API_KEY is not set");
      console.error("  - Get your API key from: https://elevenlabs.io/");
    }

    res.send({
      messages: [
        {
          text: "Please my dear, don't forget to add your API keys!",
          audio: await audioFileToBase64("audios/api_0.wav"),
          lipsync: await readJsonTranscript("audios/api_0.json"),
          facialExpression: "angry",
          animation: "Angry",
        },
        {
          text: "Check the console for instructions on how to get your API keys!",
          audio: await audioFileToBase64("audios/api_1.wav"),
          lipsync: await readJsonTranscript("audios/api_1.json"),
          facialExpression: "smile",
          animation: "Laughing",
        },
      ],
    });
    return;
  }

  const prompt = `You are a virtual girlfriend.\nYou will always reply with a JSON array of messages. With a maximum of 3 messages.\nEach message has a text, facialExpression, and animation property.\nThe different facial expressions are: smile, sad, angry, surprised, funnyFace, and default.\nThe different animations are: Talking_0, Talking_1, Talking_2, Crying, Laughing, Rumba, Idle, Terrified, and Angry.`;

  let messages;
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });
    const result = await model.generateContent(prompt + "\nUser: " + (userMessage || "Hello"));

    try {
      messages = JSON.parse(result?.response?.candidates?.[0]?.content?.parts?.[0]?.text || "[]");
      if (messages.messages) {
        messages = messages.messages;
      }
    } catch (parseError) {
      console.error("❌ Error parsing Gemini response:", parseError);
      messages = [];
    }
  } catch (apiError) {
    console.error("❌ Google Generative AI API Error:", apiError.message);

    // Check if it's an API key error
    if (apiError.message && apiError.message.includes("API key not valid")) {
      console.error("🔑 Your Google Generative AI API key is invalid or expired.");
      console.error("📝 Please get a new API key from: https://makersuite.google.com/app/apikey");
      console.error("⚙️  Update your .env file with: GEMINI_API_KEY=your_new_api_key");

      res.status(500).send({
        error: "Invalid Google Generative AI API key",
        message: "Please check your GEMINI_API_KEY in the .env file",
        instructions: "Get a new API key from https://makersuite.google.com/app/apikey"
      });
      return;
    }

    // For other API errors, return a generic error
    res.status(500).send({
      error: "Google Generative AI API Error",
      message: apiError.message
    });
    return;
  }
  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    // generate audio file
    const fileName = `audios/message_${i}.mp3`; // The name of your audio file
    const textInput = message.text; // The text you wish to convert to speech
    await voice.textToSpeech(elevenLabsApiKey, voiceID, fileName, textInput);
    // generate lipsync
    await lipSyncMessage(i);
    message.audio = await audioFileToBase64(fileName);
    message.lipsync = await readJsonTranscript(`audios/message_${i}.json`);
  }

  res.send({ messages });
});

const readJsonTranscript = async (file) => {
  const data = await fs.readFile(file, "utf8");
  return JSON.parse(data);
};

const audioFileToBase64 = async (file) => {
  const data = await fs.readFile(file);
  return data.toString("base64");
};

app.listen(port, () => {
  console.log(`🚀 Virtual Girlfriend listening on port ${port}`);
  console.log(`📱 Frontend should connect to: http://localhost:${port}`);

  // Check API key configuration on startup
  const geminiApiKey = process.env.GEMINI_API_KEY;
  const elevenLabsApiKey = process.env.ELEVEN_LABS_API_KEY;

  console.log("\n🔑 API Key Status:");
  console.log(`  Google Generative AI: ${geminiApiKey && geminiApiKey !== "-" && geminiApiKey !== "YOUR_GOOGLE_GENERATIVE_AI_API_KEY_HERE" ? "✅ Configured" : "❌ Missing or Invalid"}`);
  console.log(`  ElevenLabs: ${elevenLabsApiKey && elevenLabsApiKey !== "YOUR_ELEVEN_LABS_API_KEY_HERE" ? "✅ Configured" : "❌ Missing or Invalid"}`);

  if (!geminiApiKey || geminiApiKey === "-" || geminiApiKey === "YOUR_GOOGLE_GENERATIVE_AI_API_KEY_HERE") {
    console.log("\n⚠️  Google Generative AI API key is not configured!");
    console.log("   Get your API key from: https://makersuite.google.com/app/apikey");
    console.log("   Add it to your .env file as: GEMINI_API_KEY=your_api_key_here");
  }

  if (!elevenLabsApiKey || elevenLabsApiKey === "YOUR_ELEVEN_LABS_API_KEY_HERE") {
    console.log("\n⚠️  ElevenLabs API key is not configured!");
    console.log("   Get your API key from: https://elevenlabs.io/");
    console.log("   Add it to your .env file as: ELEVEN_LABS_API_KEY=your_api_key_here");
  }

  console.log("\n");
});
